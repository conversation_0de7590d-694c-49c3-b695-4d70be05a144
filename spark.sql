-- MySQL dump 10.13  Distrib 8.0.33, for Linux (x86_64)
--
-- Host: localhost    Database: spark
-- ------------------------------------------------------
-- Server version	8.0.33-0ubuntu0.22.04.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `behavior`
--

DROP TABLE IF EXISTS `behavior`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `behavior` (
  `behavior_type` text,
  `num` bigint NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `behavior`
--

LOCK TABLES `behavior` WRITE;
/*!40000 ALTER TABLE `behavior` DISABLE KEYS */;
INSERT INTO `behavior` VALUES ('3',8560),('1',283516),('4',3210),('2',4714);
/*!40000 ALTER TABLE `behavior` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `meal`
--

DROP TABLE IF EXISTS `meal`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `meal` (
  `MealID` text,
  `num` double DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `meal`
--

LOCK TABLES `meal` WRITE;
/*!40000 ALTER TABLE `meal` DISABLE KEYS */;
INSERT INTO `meal` VALUES ('B00I3MPDP4',1973),('B00APE00H4',1858),('B00DAHSVYC',1760),('B00I3MMN4I',1541),('B00CDBTQCW',1494),('B00B8P8O9K',1423),('B00I3MNGCG',1401),('B009FZFONO',1308),('B006Z48TZS',1275),('B00I3MNVBW',1245),('B004MWZLYC',1178),('B00CDBR1P6',1133),('B005544TRQ',1108),('B008QTTGGG',1068),('B00F87ZUYG',1028),('B00D5OZQUC',992),('B00I3MMTS8',980),('B00DTOYIIE',976),('B00DQISQX6',893),('B008BQ8VOC',890),('B00H7NDSPC',810),('B0099JKR6U',808),('B008BQG3RE',785),('B00F0XPJH6',763),('B009IJJ5B4',752),('B00C7KXUOE',751),('B007JF83YY',715),('B008JSO21S',669),('B00C0OLT6S',669),('B009SJ3GUK',645),('B00I3MMQYK',610),('B008EQHT4M',599),('B009JARQ3G',592),('B009OYSFDM',586),('B00BLCHL4Y',567),('B00FOILOSU',562),('B00CDBQNZU',558),('B00D1N6OQ2',550),('B00F3WB7FM',547),('B00BW75MKI',543),('B00ESNKDHA',538),('B000VU4GW2',511),('B003RRW3BC',507),('B001EXRQQ8',499),('B00EY7LT9Q',492),('B00CBNOD0W',475),('B008X0SGDC',463),('B00F406S2U',460),('B00DZJIRFI',454),('B00BLCHYKU',450),('B000OGTRC2',445),('B0040HNZTW',440),('B00F49E8O6',434),('B00I9AGNQS',424),('B009O6QKKK',423),('B004126A1G',417),('B00CDBTVM2',413),('B009IF7092',405),('B009GEDP26',404),('B009JZTOG8',403),('B008ZXSUO2',394),('B0079W7X98',392),('B00B2G2RG6',392),('B008JSO786',390),('B009GX6F10',382),('B001QDKK3W',382),('B003B63FKW',380),('B002NWNTL0',376),('B003V61JUK',375),('B004GTOKM0',375),('B00F3KFFPM',370),('B003NRWVMC',367),('B000MVN8GK',364),('B00D5P0QMY',364),('B00DBT2QZY',364),('B0025Z8G4K',357),('B00CDBQ9B8',355),('B00ETKG8AI',353),('B004GEYL9M',347),('B0079W7OCY',345),('B00D1NBQ60',343),('B009AP3HQK',343),('B00F38ELNG',337),('B00FEC4B54',337),('B009K0OL06',337),('B001F6ZIXC',336),('B00CBNOBYU',331),('B005C4FD0Q',329),('B00APRBBBK',324),('B0012QRPU4',323),('B00EY8MCOQ',323),('B003NRVE6Q',322),('B00IK96YXU',321),('B00D9JUWDO',321),('B004TJRCNG',309),('B000MVN8HE',309),('B00A2KIMH6',304),('B009CP7SQI',303),('B00ESNDHQY',300),('B0055MQUM0',296),('B007ORTYKO',289),('B00CBNO8MK',283),('B000OJFV3S',281),('B004X2M3N8',277),('B00DVFR8QA',275),('B00L86ZKAK',271),('B004RZMQCE',267),('B009K0ZGJQ',266),('B0083IJGBU',266),('B00FJ01MQC',266),('B005D63I78',266),('B008947J8U',266),('B008X0SSDU',265),('B009IG59DK',264),('B002KGEORM',262),('B00DYQAT8K',261),('B000JO9JHW',261),('B007UMK3SA',260),('B005GT575S',257),('B007QRER4K',254),('B00F55HIRI',254),('B0045XR7O0',252),('B00FMO8DXU',250),('B0054U565U',249),('B000UVUMDO',249),('B00FZJY336',248),('B006UJVOPA',248),('B00FVS44GC',247),('B00JIL4HLA',246),('B00366JI9O',245),('B008JSOXLW',245),('B00JMJOWXG',242),('B002KGB2EU',242),('B00FIZIY3M',241),('B00FDWGMIE',239),('B003NQ1T58',239),('B000VZUWZW',238),('B00BPDFXDA',237),('B0091P60PY',236),('B00ATLJYL6',235),('B003XU02QG',234),('B005DJRD80',234),('B00IC9X09O',233),('B00KBN95IE',231),('B0036DCJKW',226),('B00I3MN74S',225),('B004KPX56C',223),('B00932ADLC',222),('B009KHHELW',220),('B0064JGWZS',220),('B00F3W9NQ2',220),('B000S9FEFY',218),('B004X36GNK',218),('B001GRVMLM',215),('B00HMCLQMA',215),('B000I9TWV6',213),('B003UU1UTC',212),('B00DNUF7KW',212),('B0044BY98K',212),('B005OHSOL6',212),('B00H8C57GA',207),('B008XFAZRW',206),('B008LUC32E',205),('B009RURGHO',205),('B000UU4IX0',204),('B0088LTU54',203),('B00F3WOO70',202),('B006FTBWXY',202),('B00B2LCW8O',202),('B00CO0LI5U',202),('B004ZISVRW',201),('B0036D8M9E',201),('B00F3KFI4U',197),('B009F7QE0E',196),('B00CE10BD4',196),('B000MPGI68',196),('B00J7UV274',195),('B009AP2G26',195),('B00524HBY2',194),('B00A0G14DQ',193),('B00DMY24A0',190),('B004VQX4PW',189),('B003R7R1ZK',189),('B009M73DN8',189),('B00J5WENJS',189),('B000U6I0B0',188),('B001SE07JG',188),('B00IKFSSTW',188),('B0048ZXXIO',187),('B009C46UEK',187),('B00F2CESXG',187),('B002962RKE',186),('B003VDX6MW',186),('B002QS5OQ4',183),('B00ESNKFXM',181),('B00F2C1RPS',180),('B00A9Q5GR2',180),('B0087YNHNI',180),('B002Y0TGOU',180),('B00BLCHXWY',179),('B003L4S0VI',178),('B00HY7VG9G',177),('B00F6PL4G8',176),('B002A493NY',175),('B00CXI81J4',174),('B00150OTCE',172),('B00A8D18ZA',172),('B009ZQGL5I',171),('B0013FJOG2',171),('B004JM9BXM',170),('B009F11B7Q',170),('B00HD6RLBK',169),('B0036D8LPO',168),('B009PI7JMU',167),('B00KPQ7TR6',167),('B00B0TNVNS',166),('B00459EZ7Q',166),('B000ZU1KMW',166),('B005CZM5DI',166),('B00AYUCDDS',164),('B006FYDKMA',164),('B00EHNMUJU',164),('B007PYFAP4',163),('B0037KH0WQ',163),('B00I3MMLHC',163),('B005F2BB3I',162),('B001EUE6X2',160),('B0054TK99E',159),('B004AUGJS8',159),('B00337ZGIS',159),('B00EE2W1GG',158),('B004BGQA8A',158),('B001CB8NQE',158),('B006YZSC3C',157),('B000WFB83Q',156),('B000P56MYS',156),('B000HAB4NK',155),('B00I2GVDVY',155),('B000WT7R6O',153),('B00APE1NZW',153),('B0073J5X58',152),('B00AHPCQFK',152),('B001MVN03K',151),('B00F49A3A4',151),('B003OURWUO',151),('B005DPJ6VQ',151),('B005KO352G',151),('B0038L1XPO',150),('B004H3N6DO',150),('B0098VVOGG',150),('B0071E6J30',150),('B00IUX32OU',149),('B000JQYE4I',148),('B00406OXAI',148),('B004MPCIMC',148),('B007G5GMMC',147),('B007QYRCX6',147),('B004TPTH56',146),('B00FL7UCB4',146),('B00BECZ6N4',145),('B0045XL3I6',145),('B0032UQR70',145),('B007PKOKZY',144),('B00JBNX59U',144),('B006Y5BWQG',143),('B00FFV9ZBY',143),('B000UU4IVM',142),('B007N4BTEW',141),('B004VJQ7F8',141),('B00BFQ1OFI',140),('B00J5U9SIQ',140),('B004BGS3C6',138),('B002N4QCTE',138),('B0011NHCYM',137),('B0046DLPWY',137),('B009L0H5OY',137),('B009GEAWOU',136),('B00F0O6YEW',136),('B008JAGM6Y',136),('B003ZVO6JW',135),('B00LG0DKBO',135),('B00I2CMJRK',135),('B00DN15JWW',133),('B001ENSOVO',132),('B000IBUIS0',132),('B00DOS59RE',132),('B00D76BEIQ',132),('B002JAU1X0',132),('B00FKXQVI2',131),('B00426035K',130),('B006RECSBC',130),('B000ICRKLM',129),('B006YP42IG',129),('B000WM9ZU2',129),('B004XFVLNI',128),('B001DD9QDK',128),('B00DGC8XEE',127),('B004UP3D8M',127),('B00H9IO0X4',127),('B00KKGS1KK',126),('B004CTBG1W',125),('B000IK882Y',125),('B00899EO9M',125),('B003MCOTT6',125),('B0095R3J8O',125),('B009IF7Z38',124),('B00DKRXM7S',124),('B0095R53M4',123),('B00CB6F1QO',123),('B003UPE4WC',123),('B00CDU5MBC',122),('B0012H0DBG',121),('B00ESB6GBY',121),('B003N1KND2',121),('B00FGLYZKO',121),('B004FEJCKQ',120),('B007LVT9SA',120),('B00DKX27OG',120),('B005CRVDR0',120),('B00CBLGJY2',120),('B001N4PPWK',120),('B00BWJD4MO',119),('B00EY7Y8VC',119),('B004VRKFB2',119),('B000I9S5BE',119),('B00HUNNYW6',119),('B005TPRNGA',118),('B002TWWOHE',118),('B00IJKZY4A',118),('B00E1BYFP0',117),('B000W4WD40',117),('B007PYFAJ0',117),('B00FBM25W8',117),('B006X41N2Q',117),('B002TUO2SU',117),('B009KHBGWK',116),('B00BB8OEA2',115),('B00B7FZE3U',115),('B0036DCJCU',115),('B0053XHZIO',113),('B004TJRCU4',113),('B0089A6BLU',111),('B008BF99F8',111),('B000HZEHL6',110),('B003KYZI5U',109),('B006W59XOQ',109),('B003YKC4BG',109),('B00EEXPDTM',108),('B00HNZHPGC',107),('B001ODQA4C',107),('B00CB6N4LS',106),('B00CDCXZWI',106),('B00JBHQ138',106),('B005JR3TUM',106),('B004UUA1YG',106),('B003PDL346',106),('B0086213XW',105),('B00944CJRK',105),('B004I90IW4',105),('B00BIFRNPG',105),('B00EY7L4QO',105),('B00HUF9R52',104),('B007427XS4',104),('B008ZXSW2C',103),('B003L4Q0UQ',103),('B0071MUKEG',103),('B00GBUQXSG',102),('B000OC2T4Y',102),('B00BI0OB56',102),('B0058BIL3O',102),('B000VU2SW2',102),('B00CB6SU5I',102),('B00899EOIS',101),('B006I0GSZM',101),('B00HNXZ1EM',101),('B00JGMK6VG',100),('B0021Y8RW6',100),('B00IT34WC2',100),('B000WFFAIA',99),('B002SDOI14',99),('B004YVAD6M',99),('B0095IDWG2',98),('B007JF8G9Q',98),('B009HNDCC4',98),('B00IR6IB1E',98),('B005KP7EK4',97),('B005M4QA12',97),('B00D2D7X52',97),('B008621DSM',97),('B009QW6A0M',97),('B00HQQOLSS',97),('B000H4YNM0',97),('B00ESND9HQ',97),('B00DGN97VQ',96),('B00574P75M',96),('B002H0HZQS',95),('B00E1BF0KE',95),('B001V80OP6',95),('B005JR3Y5W',95),('B002EVGVBA',94),('B00ESB6CV8',94),('B00IVMTL9K',94),('B002TNS012',94),('B00627V4UU',93),('B007C7UI8S',93),('B00F0CLHQO',93),('B009PK3BZ2',93),('B00FS6COPU',92),('B005PUXH3W',92),('B0061IMTYK',92),('B007Y36DVG',92),('B005HFXHIU',92),('B00IA2FABE',92),('B00ETRANIO',92),('B008EYQQPM',92),('B00AVTLUD6',91),('B00B6870JE',91),('B004DMOJLW',91),('B000IXUOP0',91),('B005P58OZ8',90),('B001JGECT4',90),('B004BHISOI',89),('B00D844FUQ',89),('B00FDZ8S2O',89),('B00C0OO294',89),('B0073CMRKE',88),('B00F0SWPSC',88),('B009GX69RK',88),('B001PN63PC',88),('B002IVNLKA',88),('B005EPFA8I',87),('B005O718OQ',87),('B00978SIJ6',87),('B00CO0LNIM',87),('B00B8C6JHM',87),('B00CO0LNRS',86),('B00FEB06WM',86),('B00CB6PDSA',86),('B00JH1IDDY',86),('B005OPTRW8',86),('B004AZ5QG4',85),('B0089U12GO',85),('B003NS0O70',84),('B00D6BR75Q',84),('B00F34YH5C',84),('B00DN7LSXU',84),('B000W5KBI4',84),('B007CGLRWA',84),('B008ET98Z2',84),('B00EZJUPWA',84),('B00I3M1IA8',83),('B0087YQVY0',83),('B00KF7NNTI',82),('B00A2M9SEK',82),('B009I7VS2K',82),('B00D845AQO',82),('B0037NCNF2',82),('B005JR4B54',82),('B001G8MP9Y',82),('B007MHB990',82),('B00F2CE91W',82),('B00IJNKQHM',82),('B009TKQREA',82),('B00F6PKYX2',82),('B000MVIAUY',81),('B007WPLZAK',81),('B001BXQ97I',81),('B003EY62H4',80),('B000VU8DCQ',80),('B0071UYJFE',80),('B004PFF6EQ',80),('B001ALX4I8',80),('B001V98S2G',80),('B00AJGAOM4',80),('B001V58C4O',80),('B00DDKY4OC',79),('B007YZ29U8',79),('B0037L6BHK',79),('B00BMT8TYW',79),('B003YF84MY',79),('B00JH3S0AI',78),('B007PYEWZ8',78),('B006SV4QFK',78),('B004MSP04C',78),('B0089JDMHW',78),('B002L4BQ42',78),('B003ZHOWFY',78),('B003QMJJCY',78),('B00I3MN9JG',77),('B0021HAG5E',77),('B000J1B1PI',77),('B002QNBRYM',76),('B00EM6MGHI',76),('B00CB6NINM',75),('B0047G01H0',75),('B002R9LQN2',75),('B0064YPMOU',75),('B005P58PIO',75),('B003N1DFUU',74),('B000IKQX44',74),('B00FFV9VAO',74),('B00KPZIABG',74),('B00B3CJWTE',73),('B00B8C72OG',73),('B005PK5KP0',73),('B002KF3U8M',73),('B002BLCNHY',73),('B002CHLG84',72),('B008XFAE8M',72),('B00FWPDIKW',72),('B002AL4A4E',71),('B0063ONN9C',71),('B0068ZCCA2',71),('B00DDKXECU',71),('B003AVGMV2',71),('B005GNQ1J0',71),('B00C7POSC2',71),('B00BHLTQ6U',71),('B00KDO42CU',71),('B005749LXQ',71),('B00IQ3XNLQ',71),('B001Y5913C',71),('B00GOQANS8',71),('B009HNCUYK',71),('B00ESB15HY',70),('B00GCET720',70),('B005T5N0MQ',70),('B004Z4QUB0',70),('B00HQMWE40',70),('B00J5TCASC',70),('B0051OWS2I',70),('B000W4BJ7C',69),('B000VU6K0I',69),('B00KYZMSQ0',69),('B000MMX5E4',69),('B000IJRK7O',69),('B00CKJQVSE',69),('B0090EJZA8',69),('B00FVS3NFA',69),('B00ICGZIKG',69),('B00DYZGII6',68),('B00AYRIFH4',68),('B003925164',67),('B004ML1HY6',67),('B00751IYQ4',67),('B00B2EX4C4',67),('B00BMFN6ZI',67),('B00C70XZJE',66),('B001JG5PJU',66),('B009KOE4ZO',66),('B000W5NSNE',66),('B006Y5AV68',66),('B00F3KL23Q',66),('B00ESNDG8I',66),('B00LCHENE4',66),('B00BP4WIUU',66),('B004D0733A',66),('B00APPB766',66),('B004BHGDYK',65),('B005PK4YNO',65),('B008LRAS5Q',65),('B009PI7EIE',65),('B0041R9PX6',65),('B001CH44YI',65),('B0026QMYN2',65),('B00EYY6JHG',65),('B008FWHGYS',65),('B00BAP3J3Y',65),('B007A1DSQ0',64),('B007G5H2X0',64),('B005OHSRWC',64),('B00BX3X4IS',64),('B004HTAGOK',64),('B006W59O6I',64),('B00C2UG118',64),('B002IAAS2U',63),('B00DLDM8HG',63),('B00AX4QEAS',63),('B002JPC4P8',63),('B00DVN14XU',62),('B008LRB1O8',62),('B00E8ANEQA',62),('B005UVNEIE',62),('B00BN4V2YA',62),('B002KEZ91E',62),('B000J0Q1KO',62),('B000IKP5AM',62),('B005OV0LJA',62),('B003AI9Y7Y',62),('B00252MNWY',62),('B00GUPFO5U',62),('B006O3Z39U',62),('B00G2TAAVM',62),('B003A3PWFW',62),('B003MAA5J6',62),('B006TA764S',61),('B00EM76GQY',61),('B0056RKA1G',61),('B00HALOZIA',61),('B00BCWNXG8',61),('B001TNVEKC',61),('B00D5PCXI4',61),('B006UAWH2S',61),('B005OHSTFM',61),('B0095R3ES4',61),('B007UMJSZE',61),('B003NJJ3PS',61),('B008Y0I0HI',61),('B002Z01XOU',61),('B007427K8W',61),('B00GP74EX6',60),('B005KO31OI',60),('B008EPHB28',60),('B00AHSFQMM',60),('B0050WFI2S',60),('B001ENOB8Y',60),('B002029Q4C',60),('B007QJD6R2',60),('B00ESB14V6',60),('B0087BJXM0',59),('B00KV8A68W',59),('B001EUKHRG',59),('B002KETO0Q',59),('B004K0HGIU',59),('B008V6Q8G0',59),('B000UERE3M',59),('B00HQBOL60',59),('B002NUXBLA',58),('B00IRKCMHO',58),('B00EY7LTJG',58),('B000ULTV4U',58),('B004SKLP70',58),('B00GO8KFJS',58),('B0051QREPW',58),('B0015TFUSC',58),('B0086T706Y',58),('B00627UX7U',58),('B00KOA8UXU',58),('B00IMYS1PC',58),('B00D5P1F8S',57),('B00GLYM3QS',57),('B009SHO0DY',57),('B00BXKH9QE',57),('B00GMA9SKK',57),('B00EZJUDFE',57),('B00627UPDM',57),('B00870YQLE',57),('B007B9Q8K4',57),('B008S9CU7G',57),('B00FJN9EL4',57),('B000W4WSCC',57),('B0071IDK88',57),('B006VJ7TC6',57),('B00F4PKH5E',56),('B006NU7C48',56),('B00DKSAIJM',56),('B00ENYLR8I',56),('B00GNL4IY4',56),('B009K0ZLIM',56),('B00669UD0Q',56),('B00CNB2UKW',56),('B007O2TG6Q',56),('B00D6PJWO6',56),('B00DCLZMQG',55),('B004F87GI2',55),('B003IHWLO0',55),('B004CTUECE',55),('B004H3H270',55),('B00DIX3FIK',55),('B00AEFVNFC',55),('B00FOIM524',55),('B00332YHLU',55),('B007H63DRW',54),('B00DYGTXUU',54),('B00DIX2KFO',54),('B003B4SH5M',54),('B007ZF1JW6',54),('B00FC3K3UW',54),('B004EKF0QQ',54),('B00CZ5P2HY',54),('B0085SOJ1A',54),('B006DUK6SM',54),('B00DYPQYZ8',54),('B00GCEVU3E',54),('B00JRSBG9U',54),('B00C5O60PS',54),('B00BT4MT8W',54),('B003AIE43I',54),('B004D1VQU0',53),('B008JSNIQS',53),('B00L33AF7G',53),('B00DXI1R44',53),('B006LZSF8M',53),('B00IHC9CIE',53),('B00DY87WQA',53),('B00BOKLAJU',53),('B0038ZEWY4',53),('B0054IAZYE',53),('B00IAKMVHM',53),('B008FWHMMO',53),('B00BWTSZXW',53),('B00KKGRT1W',53),('B0046B2ODA',53),('B001GQG07O',52),('B004I1I914',52),('B00CQ1UXN0',52),('B009K72EP8',52),('B00FBMQ68M',52),('B00B23TA28',52),('B00G7Z0T8K',52),('B00F2PH3F8',52),('B002T33IAA',52),('B00A6LSZFU',52),('B009RLX2G2',52),('B009CH0T9O',51),('B00ICGY6R2',51),('B0066SB8UQ',51),('B00ICGYECE',51),('B00J002TKA',51),('B002ICFX0U',51),('B00B13UW0I',51),('B007MJSPJA',51),('B00AO1SNF4',51),('B00BQ92SCW',50),('B004G7CRUY',50),('B00BOLQ6CU',50),('B00EZJMKCI',50),('B00751JL2K',50),('B007PYEXL6',50),('B00IYH8UEY',50),('B006O2WRHM',50),('B008EM1DAC',50),('B0070TEN7A',50),('B002STKILW',50),('B005JR3PMY',50),('B006LG7IJ8',50),('B007PKOL62',50),('B00APE06UA',50),('B001JB3T02',50),('B00HSUOZS8',49),('B00D6BR9EU',49),('B006FKSSOY',49),('B008O2QERY',49),('B000WHIIO6',49),('B001NJO76U',49),('B002LS7YVM',49),('B00D69JGRK',49),('B00B3A0I6M',49),('B004BTR8JW',49),('B004BHEC8O',49),('B002S28HUS',49),('B00K1WIN7O',49),('B003BF1ZRI',49),('B002IEY0LQ',49),('B00FKFGZZO',49),('B008HZEJ5W',48),('B00B45ABKO',48),('B00CW87CFO',48),('B00DSXI5BW',48),('B0013Z0PLA',48),('B00627ULUO',48),('B00I4QRAUU',48),('B00BS4MQ0O',48),('B004N8OT0C',48),('B001SE1FAG',48),('B0080CK0O6',48),('B00E7NCWY8',48),('B00DU6NVU2',48),('B004CTUP28',48),('B00241ASBO',48),('B001OHU9VS',48),('B000IO3OBK',48),('B00GNEXHXY',48),('B00ITKS65Y',48),('B00IOGX55O',48),('B00A3S4XVQ',48),('B00ALQT6MG',48),('B00BMAV33K',48),('B008DIY9T4',48),('B00CO0DUYM',48),('B00ADHOMZ4',48),('B00AV605D0',48),('B00D8VS7GC',47),('B002UXAEV0',47),('B00C35F1G8',47),('B007QRJV0K',47),('B002VVCMTS',47),('B00BBL297Y',47),('B008N6X3BG',47),('B007UXSDT0',47),('B000W0H1EG',47),('B009CZ4QMW',47),('B0055EQ30M',47),('B000W4Z5ZO',47),('B00JQ306BK',47),('B005VA7M2I',47),('B005DD7LR4',47),('B004WMKKWK',46),('B00I3MNERI',46),('B004263SAM',46),('B00A65FXH4',46),('B0046XJEXG',46),('B000IBUP6A',46),('B00IID0NPI',46),('B000VVEDWO',46),('B008620ZTA',46),('B00HQRCBP2',46),('B00627UN0C',45),('B00BKVI8XO',45),('B00C3C0QB6',45),('B009HX0FM4',45),('B001UPP900',45),('B00AOIJRQQ',45),('B0055XI8BK',45),('B003FGNS9Q',45),('B004FTMNN4',45),('B00H8XP4W6',45),('B001HWT3TE',45),('B00H294Z2Q',45),('B003QH7E94',45),('B00IKT36S6',44),('B00A2KV89U',44),('B003BOQVF0',44),('B00BUJ5YHO',44),('B009WJXC0U',44),('B003Q97D48',44),('B00I8YKI02',44),('B009ZQ7M7O',44),('B00BQKOV30',44),('B001G8U0VY',44),('B00AQAEF7S',44),('B00BN4QKU6',44),('B0098G756O',44),('B00KZ7XOKQ',44),('B00EY89MWG',44),('B001GQHV36',44),('B00IGDHQEQ',44),('B000H2DMME',44),('B00AH8SPOI',44),('B008V90WK0',44),('B007NYO4GM',44),('B00JP260B2',43),('B000U6YXPM',43),('B006U3MY7S',43),('B00AF6073Y',43),('B00CY9YE66',43),('B001M432XA',43),('B00HNEBLCC',43),('B00C18X4HA',43),('B0062246R8',43),('B00BLBTEPY',43),('B001V52RZE',43),('B00FMO7TSA',43),('B00HASYDK8',43),('B00FOILB0G',43),('B006Y5B2XE',43),('B00F6F2U5M',43),('B00E2OTIIK',43),('B00DX6Q3VI',42),('B00BG69SEQ',42),('B009HONRO6',42),('B000LVIIUC',42),('B000V5Q8JQ',42),('B00AYT159C',42),('B007O62JG6',42),('B000I5PVD8',42),('B006OU14BO',42),('B003DNTEBC',42),('B003RG3ND0',42),('B00G32MX9U',42),('B0088OUTLA',42),('B004520KTU',42),('B00D2GR3TK',42),('B00CW88HXK',42),('B00EE2W7JM',42),('B002KGFK52',42),('B002MZKACY',42),('B006WC63X8',42),('B00B19DDOY',42),('B007L8N42A',42),('B000WMGZ8M',42),('B009VB803S',42),('B002BEXZ32',41),('B001SRN6IW',41),('B004IY9DQQ',41),('B00CGUCACI',41),('B00E59CJQ0',41),('B0064YPHEK',41),('B00B47LUZ2',41),('B00FDMV7IY',41),('B00ECU20HA',41),('B00CBRH2OW',41),('B007UXTC2M',41),('B006VA3X9I',41),('B00BR4XH7Q',41),('B000W5R3QC',41),('B0047YZISE',41),('B00LPWPMCS',41),('B0030I61FW',40),('B0013FLHEE',40),('B00BB8P8T8',40),('B004IJQ792',40),('B004LK8K66',40),('B004KCIO52',40),('B005W8IKCU',40),('B003T0M4U2',40),('B00J8CFQ2S',40),('B00IAKL5S8',40),('B000W5PIYQ',40),('B009KH9XX4',40),('B005WGBBS2',40),('B001QDDILI',40),('B007RV6X2Y',40),('B00FGN2HZW',39),('B008GJVAW4',39),('B004WZ6KXA',39),('B008XFBQNO',39),('B002BSC29Q',39),('B00EUFOZ52',39),('B007HGY0S8',39),('B00599IU22',39),('B00C18NM54',39),('B006H0602Y',39),('B00IS8Q5HS',39),('B004L3OAAS',39),('B003XLHT1G',39),('B003TVS5C2',39),('B00ASM0RL2',39),('B00C5K6Z48',39),('B003E8ZYGK',39),('B003VXMFY2',39),('B002AB1PZQ',39),('B00B09HOGI',39),('B00BMT9EMI',39),('B00JXCZIFI',39),('B001HBLE2O',39),('B00D8VGN8G',38),('B006VEFUJA',38),('B006DUK1K0',38),('B004WYXHHI',38),('B00C18VOJK',38),('B009LZCN5K',38),('B005MW94QC',38),('B00EY7YGBO',38),('B002M5JY9O',38),('B00FEAZPSS',38),('B009KZYXKY',38),('B001CNRBEC',38),('B00CDBQP58',38),('B00BTWG9R6',38),('B0018SOVIA',38),('B000Y0S72O',38),('B004B8O9GI',38),('B00AZRLEVW',38),('B004AJ1CKE',38),('B00J5SGZUM',38),('B00KPQ862S',38),('B00DGMVMCY',38),('B009C46X8I',38),('B00B0TNY3K',38),('B00ISC7LQS',38),('B00KCXHNRS',38),('B00H9N22IO',38),('B00BG2TX9A',38),('B00F0CLD5E',38),('B004AJ36NA',37),('B008F9SM0S',37),('B001W1Y5RK',37),('B00GP6YH8O',37),('B00DR98H4C',37),('B00A1TV8F6',37),('B00AYALPLO',37),('B00LIRFK5E',37),('B00BMKNPFY',37),('B002QEA7BA',37),('B003YUGS6S',37),('B003IHN8UG',37),('B0058SH8F4',37),('B009GXUMI2',37),('B00C3WI6WM',37),('B00F2PHIGC',37),('B00HSEZ49I',37),('B00BN4PXO0',37),('B004WOZ1F4',37),('B00GGC4PCA',37),('B0081HVXG4',37),('B001VT4L7W',36),('B008POASKK',36),('B000NHRTAO',36),('B00CE0ZW9I',36),('B006X6JHH2',36),('B005HEFE28',36),('B007OV0UYY',36),('B00HNEBCHG',36),('B00CW842PM',36),('B002C4Y3DC',36),('B004ATLR9K',36),('B00C36TYYM',36),('B00HBJ0HEM',36),('B001ENLHX6',36),('B00AG8VPTG',36),('B004CAGDQO',36),('B00EC7VQX2',36),('B00I2JFHRW',36),('B00H8BPJTQ',36),('B0092ME70Q',36),('B00GK51FO4',36),('B007Y36M26',36),('B004D24SZ4',36),('B00L4DXKEU',36),('B00L9MZFOO',36),('B00CFVNL94',35),('B00ABD0AOM',35),('B00IWLJD4I',35),('B000W4Z6XK',35),('B00IOH83AK',35),('B005WM5R40',35),('B003IB91QW',35),('B003AI2E8K',35),('B000W0A6AW',35),('B00D8VN1PY',35),('B004M1UERW',35),('B005KOAU7O',35),('B004QUD8KE',35),('B006RZZDU4',35),('B00E3P3JG0',35),('B00CU71AY6',35),('B000ULZLYY',35),('B003BYDUPY',35),('B00CEHMFUK',35),('B00K7WU85I',35),('B003336P1Y',35),('B006LWHBDA',35),('B00CD4WBMQ',34),('B00DI2NTK0',34),('B00B0FDEMU',34),('B00BBVAQME',34),('B00EUME3DO',34),('B0049AFWKK',34),('B003NRYJDQ',34),('B00B46XTDY',34),('B000I8FOZA',34),('B00A8P9AG2',34),('B003UMJTPW',34),('B000J46A6K',34),('B0086T6RH2',34),('B009X5BDT0',34),('B00HPPVNHM',34),('B002UJRM64',34),('B00BWUK6DS',34),('B003TJKRXY',34),('B00D3MQ2DQ',34),('B003YDOYVG',34),('B003S7JPSU',34),('B0079XCKAE',34),('B00FEAY0FM',34),('B00FEB0212',34),('B003X1FPQM',34),('B0070XUDOS',34),('B001NJO792',34),('B0098VVF1A',34),('B006GM4K2A',34),('B0033YX6ZQ',34),('B00EJLRV6M',34),('B00D3FT5VO',33),('B006LYWJR6',33),('B005HBT5VC',33),('B009651C9S',33),('B003BOWF64',33),('B006Y5C5XA',33),('B008TCF7WW',33),('B00DW2RAB0',33),('B00B19DSFS',33),('B00C361C24',33),('B005TEZV74',33),('B004YVA9S4',33),('B00B19GYCW',33),('B002H05O48',33),('B007A1DUYA',33),('B0040H8FBU',33),('B00GJVBRGU',33),('B00CHVPEEW',33),('B00CBKUWKA',33),('B000V5Q9Y0',33),('B00CRWPGQ2',33),('B009JZSKRM',33),('B00BMFN9BO',33),('B001NJQ1YQ',33),('B00L2GPYKW',32),('B004BN7CGW',32),('B00AVXYXRM',32),('B007PYF66W',32),('B0057UGRGY',32),('B009XPFV9S',32),('B0090X4IYW',32),('B009HNDGLG',32),('B001U7TJXQ',32),('B00HZKJK0Y',32),('B00DIXFXHQ',32),('B006Y5GHU2',32),('B005PK5OEM',32),('B00DGMW7P0',32),('B001ENNJSC',32),('B00BKI9048',32),('B00BWTIXFC',32),('B001YDZYE4',32),('B009MUWEJE',32),('B004IZ1V1A',32),('B00BMAR7YO',32),('B00D1N8BYK',32),('B00CW8CJHK',32),('B00J004EP8',32),('B00AJMXSG2',32),('B00C2MJARS',32),('B00AA7OI5G',32),('B0093QLGO6',32),('B00ESWOCVO',32),('B004HBOLJY',32),('B003HIC3ZW',32),('B005KM0PZS',32),('B006V644AO',31),('B001ENS0BS',31),('B00B3OQK9M',31),('B00HQSWMS2',31),('B006Y5D5QG',31),('B007943UZ2',31),('B00CRWORX0',31),('B00EAI9R1Q',31),('B007BECHEK',31),('B005G190U4',31),('B00ATF4P8O',31),('B00K7WZVXC',31),('B007HYMHEE',31),('B000OFME82',31),('B00BPFGI0K',31),('B00EUVCCWE',31),('B00A9QGKTK',31),('B00BJRB6I8',31),('B007BECIVC',31),('B003CWHG8M',31),('B004D8JQ4Q',31),('B005XUC8XY',31),('B003CXP5O8',31),('B00C58CP0I',31),('B0045TGRSG',31),('B00EDG3GQ2',30),('B0047C8BGW',30),('B001I8O2MK',30),('B00KDMEHWW',30),('B008R6LI0U',30),('B00IGDH9W0',30),('B00CHU542K',30),('B00FJ04ZWK',30),('B002T33I64',30),('B0041H3TQA',30),('B008DGRDZ8',30),('B0072C8RPE',30),('B00CE10AP8',30),('B00KEOOBUC',30),('B00EPCKUDG',30),('B004YLCI74',30),('B009L46WWQ',30),('B0041GKKNG',30),('B007UXS5NY',30),('B009PNW4LG',30),('B005K1MJ4E',30),('B008DR2ACI',30),('B00APUQLYO',30),('B00IVQ3ZH0',30),('B005JR3JC0',30),('B00HWPYPA2',30),('B00HWPZCZE',30),('B00FEAYDPO',30),('B0047L75W4',30),('B00FL0CN4U',30),('B008CS6WJK',30),('B004CD4J44',30),('B00CLE9WHK',29),('B00FLNQOAQ',29),('B007NEZ1L4',29),('B009F6PNGQ',29),('B003UOHP92',29),('B001GIIRYQ',29),('B0082CXI34',29),('B0040XE8KG',29),('B00CGFHCRQ',29),('B004N2LUXM',29),('B00FPH3W6M',29),('B00ID4I2HS',29),('B0012F6C72',29),('B00AVTLVY4',29),('B0057UGEUS',29),('B00BSXN46U',29),('B004SKQHYQ',29),('B007YQ5D8M',29),('B007CGN1JM',29),('B004I0C0QK',29),('B005DD7J8K',29),('B00FEEQVUK',29),('B001R4175U',29),('B00DEJWI2W',29),('B000UUEEIE',29),('B009VW5BLQ',29),('B009DNVLH6',29),('B0038KNRAO',29),('B00DXF6LMU',29),('B000I5RENI',29),('B00FEB0FFU',29),('B001GRVAUU',29),('B00FKOYN40',29),('B00EHD5X3U',29),('B009M9GI06',29),('B00CO0KMW0',29),('B000WFN3XY',29),('B004HY87C8',29),('B00C18X5GK',29),('B004P0WM84',29),('B00K5M7LSC',29),('B004T9BAPC',29),('B00H8V0FSQ',29),('B003HIC4H4',29),('B003WSA0TI',28),('B00CW8CWOA',28),('B0090XDE2Y',28),('B008494L22',28),('B0087YS8NC',28),('B00D9KL4YE',28),('B009L99U34',28),('B005PKQDXI',28),('B008ZXT2LM',28),('B00A05WQ0C',28),('B00AO7JASM',28),('B00FEPX6QQ',28),('B007ZU23GC',28),('B00HMVFAIM',28),('B001EXO256',28),('B009YMLO00',28),('B00K59EL6K',28),('B00DLWQ6O8',28),('B00CK3SHY6',28),('B00JWMPH14',28),('B001O3COHY',28),('B00GK5WGBU',28),('B00FEAY4XU',28),('B00H7VM5UI',28),('B002JTLLQC',28),('B005PK4ZHY',28),('B00FY2VS0A',27),('B00266MPF4',27),('B00AWM9RX2',27),('B004QMESVU',27),('B009VSK00W',27),('B00CY8GNK2',27),('B00HN1IM90',27),('B00ENYR02A',27),('B004FK9CTQ',27),('B005D9LD6I',27),('B00AFUFBT0',27),('B003EYBOBS',27),('B009MQVO0S',27),('B005JR3S0I',27),('B00H4UG71A',27),('B00B0K9PKU',27),('B00A32XYBC',27),('B003VPM6TO',27),('B003627X4U',27),('B00K0CD10Y',27),('B00FDZPC46',27),('B001R2NP66',27),('B00BL0LZL6',27),('B005PKNIO0',27),('B00G5R9DZA',27),('B005UKJW5O',27),('B004VB5464',27),('B006GM4JNU',27),('B00ENBSGFS',27),('B00JFQ96F0',27),('B00F91Q6QM',26),('B008GQW1CU',26),('B00BN4S07C',26),('B00FGM0IEU',26),('B00BDC29S0',26),('B0064YPSRG',26),('B00ARAN1SG',26),('B003P8ZZVI',26),('B000N8M43U',26),('B00C9O3C0U',26),('B0063BGBO4',26),('B00J4FQNTE',26),('B00C193T74',26),('B00KWGDBB2',26),('B003TJUEOQ',26),('B0082JW05O',26),('B00IIVXMR6',26),('B00L1R7Q76',26),('B00A15H4Z8',26),('B005MRUB8C',26),('B00JRU8QGO',26),('B00KGUJUXC',26),('B0081FD7BA',26),('B0074279LU',26),('B00870ZMEE',26),('B00B0TO5DS',26),('B00C0OLPK8',26),('B00F35DMFM',26),('B00AEFSISW',25),('B00APE00ZG',25),('B00I9M44E4',25),('B00EHANS0I',25),('B00HUKCIAS',25),('B00CD58R6Y',25),('B00BENM4JC',25),('B00E712NYO',25),('B00AYU5ULA',25),('B00BOLEKKA',25),('B00A1ZUZ0Y',25),('B00FKQRYTE',25),('B00IIJJKI8',25),('B007WW7GH4',25),('B00D409KGI',25),('B00ESB5ET4',25),('B00C18VDZ0',25),('B00GMTLB5G',25),('B001R2XQ8S',25),('B002SLIATC',25),('B0051HIK04',25),('B009KZYJ62',25),('B00D6MQ6ZM',25),('B007MS58AA',25),('B003ULUF9W',25),('B00H2E66H8',25),('B00A1ZV2KG',25),('B0026MLCRU',25),('B004E2BY8W',25),('B00GCG92J6',25),('B00EIQJH8I',25),('B00KD9N9BA',25),('B00CC3CT5W',25),('B004XUMPRE',25),('B003DZ3AJ2',25),('B004AUIJ04',25),('B00EC82O8M',25),('B00KQQANQY',25),('B0083ZLR24',25),('B00A954YS0',25),('B002W0N2X8',25),('B00BR34W3U',25),('B007AB6Q28',25),('B007VQLRGW',25),('B000OC3FZQ',24),('B00B1OZGHG',24),('B00BMFHCSU',24),('B00D6BQ068',24),('B006Z0OEW4',24),('B00ARAO6SU',24),('B00EJ630FS',24),('B00KY50U74',24),('B009KT4TY0',24),('B001ATJQB4',24),('B00F2CYVG0',24),('B000RLA1KQ',24),('B0024DXFIA',24),('B00KS0CF2S',24),('B000VU2STU',24),('B000W0HCYK',24),('B000IVUL64',24),('B000I8H2ZU',24),('B002DHDIJ8',24),('B00LBVVKI8',24),('B000HKWE3O',24),('B00905RRNE',24),('B00CCZFT48',24),('B00F88GORM',24),('B00C18WT2Q',24),('B00BN4QA5G',24),('B00H8C3FTQ',24),('B004YSDQHS',24),('B0015ZS0GA',24),('B008V6Q69Y',24),('B00KMGZPRK',24),('B00HAYWNY0',24),('B007C1K6NQ',24),('B00A8D136Y',24),('B00JKOAOMQ',24),('B00ERVIDNO',24),('B003LND0BE',24),('B00627P604',24),('B00ADYXDUC',24),('B00AJUR400',24),('B00CHIM1OG',24),('B002R2G97C',24),('B0072281NC',24),('B001BXQ95U',23),('B009DNVLQW',23),('B004H75GBU',23),('B005I1S55S',23),('B00HTYTVD2',23),('B00C35BIHY',23),('B009IF63I6',23),('B00HPRPBLY',23),('B0088W9P9O',23),('B003TVS3JC',23),('B0056CCB9A',23),('B00BECZOGI',23),('B00AWMR6DA',23),('B00AIDXT5M',23),('B000WDS04I',23),('B006224I3U',23),('B006Y5DODA',23),('B0045B8FIO',23),('B000WUAZZI',23),('B008XCZ43A',23),('B007QRJRAY',23),('B00F57MNBC',23),('B004ZWHXEK',23),('B00GAMC90G',23),('B00FO3XOXI',23),('B009EGGA3M',23),('B009DB82WK',23),('B004KKA0YC',23),('B00ESB130I',23),('B001R4Q6A6',23),('B002R9NINS',23),('B00502A47C',23),('B0050SUMUU',23),('B00HBJ9XGU',23),('B00A27PMLI',23),('B00KYKI2GU',23),('B003XQK60W',23),('B001C34W2Q',23),('B00K5LYXFC',23),('B009DB8UFY',23),('B003B1TF2E',23),('B00C58QYXM',23),('B001UI8KV2',23),('B0047YXQ10',23),('B0053ZU4P8',22),('B0045TKISQ',22),('B00BWUGS9E',22),('B00CY92YHW',22),('B008CPB5K4',22),('B000H00VBQ',22),('B00HQ2GBLM',22),('B005C2UEL6',22),('B00BN4PXYK',22),('B0057M35ZS',22),('B001GDO7TA',22),('B008RUCX3M',22),('B00LCEPKBM',22),('B009NPK1IO',22),('B00AMF6OS0',22),('B001LUW8G2',22),('B00BWTMT5M',22),('B00KPQ8L0K',22),('B00BG2AE1Q',22),('B00171LH1W',22),('B005FF3U34',22),('B003VKMZI6',22),('B00I7NI068',22),('B00C6C96UK',22),('B004XQYP9Y',22),('B00DNTW8W8',22),('B002YWXMKM',22),('B007T05BSK',22),('B00DYC6AUK',22),('B00BWUKO94',22),('B002C4U7O6',22),('B0035GNSPA',22),('B00BMAR7OY',22),('B00G4CIE88',22),('B00BH9GD2C',22),('B004GLTK2I',22),('B00B1NI9TY',22),('B0045TKIRW',22),('B00CGUPNY0',22),('B00IA3TFI2',22),('B007100QP6',22),('B001HINEH0',22),('B00FWLG2MM',22),('B00H55JEHI',22),('B00CW8CHNQ',22),('B008NNY1U6',22),('B00LM493J2',22),('B00DPU3C5M',22),('B00FOILMQ4',21),('B006H06CFO',21),('B000JEZBNI',21),('B007R9XAB8',21),('B007IQWYNK',21),('B0049QKX6W',21),('B00F3JT77U',21),('B00D5P4GUC',21),('B0095DFUF8',21),('B00FD1SQY8',21),('B004KK77FC',21),('B003FGKBSC',21),('B0072QWPSK',21),('B0091P6X2E',21),('B009YDGLM0',21),('B00J5LHY72',21),('B007TADRIG',21),('B00C5LHHYO',21),('B00IRXIKA4',21),('B008XFAZ2C',21),('B002OLLH46',21),('B007CUQ82A',21),('B00BSG69FA',21),('B002NJUNMG',21),('B00FGNDU3U',21),('B00AMBHB2W',21),('B002S5TVSW',21),('B009CPH62Y',21),('B00APN5JQW',21),('B00L1WUD58',21),('B00CBRH4YU',21),('B00D63BMYG',21),('B000MVI8HO',21),('B005CL29AG',21),('B00CW8CTWU',21),('B00ESY6CN8',21),('B007YNXQXO',21),('B000I62JFU',21),('B008DZ91U4',21),('B007NYOJXK',20),('B00HARFMU4',20),('B007MXEJU0',20),('B00HJ1B90I',20),('B00BTJ8RP6',20),('B003F46RRS',20),('B009CPIQQE',20),('B003Y5QRJ6',20),('B001UUAGRQ',20),('B001R68VPC',20),('B002UC15DC',20),('B0072C8ME0',20),('B002HMJ4W4',20),('B00BFXQP46',20),('B009FJJNQE',20),('B00C35R67K',20),('B005EEBERA',20),('B001UO82K0',20),('B008EQII56',20),('B001CMQH5M',20),('B000I5Q0ZG',20),('B001OEEKU2',20),('B00AP06R0M',20),('B00CVMGQLC',20),('B00DYQ2MYO',20),('B00IKG21F8',20),('B0072N7XQC',20),('B00BENMTKQ',20),('B00HAVK4L2',20),('B00CI14OV0',20),('B008HNQ0OW',20),('B00D11SN22',20),('B00D3UVWQK',20),('B00IBJ1XOE',20),('B004XG6D1C',20),('B009NB5UXE',20),('B00JGMK50S',19),('B0084YC47G',19),('B00E3INI7C',19),('B000SXKBXU',19),('B00FOILWAA',19),('B004KK9C3M',19),('B004U8LF7K',19),('B00I9COR96',19),('B003D6IRP8',19),('B00831XJ3S',19),('B00E0H0R98',19),('B00ETKEFQ2',19),('B00H4UGQZC',19),('B005IWN5EI',19),('B00AYB0YRY',19),('B00I3IUC3Q',19),('B00HPPVJTY',19),('B002PHW9H8',19),('B005UKK9YC',19),('B005FOLGU4',19),('B004I97HP0',19),('B00DQ4N5T0',19),('B007JF9DB6',19),('B006SV5Z0U',19),('B00H8FMFT4',19),('B005YJ8ZR2',19),('B00DENPU6E',19),('B003VSTVC6',19),('B00D5P42W4',19),('B00GAGJXJW',19),('B00IG1AL8Q',19),('B008OIH7AQ',19),('B009ZQT4G6',19),('B002J4RW6U',19),('B00FB528YK',18),('B003IHWLDQ',18),('B000H0X79O',18),('B003U6OTK8',18),('B00FS6OX8Q',18),('B00AWMDFJ4',18),('B00BLH0TQQ',18),('B008HBEJOW',18),('B0071ZTQCU',18),('B004TPTY3G',18),('B00BN4R4WE',18),('B002YJN6KQ',18),('B0041FW1BG',18),('B00BIESC3O',18),('B0076V8H9C',18),('B00CD53ULG',18),('B008IYMWYW',18),('B00ALQV19C',18),('B007EMEG52',18),('B007YP4ZLY',17),('B00AOIJN7Y',17),('B00DMLRI0O',17),('B00LBVQW3Q',17),('B00JGN11DM',17),('B009OYRXBM',17),('B00JGMJ914',17),('B000H29TXU',17),('B008POAZVC',17),('B00BG4ULIK',17),('B00HD6QSM8',17),('B0080CKV72',17),('B00C35SF3Y',17),('B00IJIN72S',17),('B002UIEMA4',17),('B00A3OHFQA',17),('B00EE6I3QO',17),('B006Z8461A',17),('B0057AHO7K',16),('B004128RXU',16),('B00KYKMKKY',16),('B0016BGYYI',16),('B001QDT85I',16),('B00IYXFDL6',16),('B009VB7RRS',16),('B00APUQK7C',16),('B009D4PG62',16),('B00EILN40A',16),('B00FPH4YRS',16),('B002JJC6E8',15),('B00HSV20KC',15),('B00ESY5VR6',15),('B008V6Q2YS',15),('B006S0007E',15),('B00DM0UX2A',15),('B00DMXS1E4',15),('B007HDPJSG',15),('B00IJM5RO0',15),('B00IP45FAI',15),('B005G190ZY',15),('B00FDZ2BEU',14),('B002K76UR8',14),('B00E59AF02',14),('B004H20ST0',14),('B00K2PN9JM',14),('B00FZJWP9K',14),('B0098VV4NE',14),('B0084RZPPQ',14),('B005FYL60E',13),('B00I99TN70',13),('B001RPORJ2',13),('B004LXIPAO',13),('B00BTWGJEE',13),('B0080SSSJ4',13),('B00978LMCG',13),('B0072N8O4M',12),('B00978M72U',12),('B00HATHNXG',12),('B0029Z0QVM',12),('B00DYQC59G',11),('B00IPQAQGE',11),('B000MN4YR0',11),('B00FW5WBW8',11),('B00AAA8JSK',11),('B00742GOP2',11),('B009XOJQF4',10),('B006RXPUQ8',10),('B009LG31R8',10),('B00FEBZ6XG',10),('B009N452XY',8),('B003ECUGHI',7),('B007OKD3GC',6),('B00F55HZB2',6);
/*!40000 ALTER TABLE `meal` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `province`
--

DROP TABLE IF EXISTS `province`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `province` (
  `province` text,
  `num` bigint NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `province`
--

LOCK TABLES `province` WRITE;
/*!40000 ALTER TABLE `province` DISABLE KEYS */;
INSERT INTO `province` VALUES ('广东',8981),('云南',8997),('内蒙古',9003),('湖北',8857),('新疆',8755),('海南',8894),('西藏',8725),('香港',8716),('天津',8686),('陜西',8788),('广西',8839),('河南',8905),('贵州',8901),('江苏',8642),('宁夏',8863),('青海',8882),('福建',8580),('黑龙江',8758),('辽宁',8827),('重庆',8739),('安徽',8814),('山东',8880),('澳门',8802),('湖南',8826),('台湾',8612),('上海',8770),('山西',8997),('甘肃',8797),('北京',8837),('河北',8910),('江西',8957),('浙江',8792),('四川',8861),('吉林',8807);
/*!40000 ALTER TABLE `province` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rating`
--

DROP TABLE IF EXISTS `rating`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rating` (
  `Rating` text,
  `num` bigint NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rating`
--

LOCK TABLES `rating` WRITE;
/*!40000 ALTER TABLE `rating` DISABLE KEYS */;
INSERT INTO `rating` VALUES ('1',1718),('2',1885),('3',4475),('4',9142),('5',21025);
/*!40000 ALTER TABLE `rating` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `review`
--

DROP TABLE IF EXISTS `review`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `review` (
  `Review` text,
  `num` bigint NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `review`
--

LOCK TABLES `review` WRITE;
/*!40000 ALTER TABLE `review` DISABLE KEYS */;
INSERT INTO `review` VALUES ('太美味了，强烈推荐！',12382),('简直太赞了',6234),('非常非常好吃',3752),('很美味，推荐品尝',3721),('此味只应天上有！',2955),('味道很正',1911),('有特色，卫生',1841),('尝过之后，不得不赞',1257),('有特色，好吃',835),('基本OK',786),('还算不错',753),('一般般吧',700),('家常菜，还不错',567),('不会再试了',265),('味道还行',264),('有特色，也比较卫生',2),('性价比高',2),('好吃',2),('同事们都很喜欢',2),('每周必点的一道菜',2),('不得不赞',2),('很值',1),('家常美味，推荐！',1),('可以尝尝',1),('有惊喜',1),('味道一般',1),('好吃又划算',1),('美味，推荐！',1),('真得不错！',1),('风味独特，真的不错！',1),('家常味道',1);
/*!40000 ALTER TABLE `review` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `student`
--

DROP TABLE IF EXISTS `student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student` (
  `id` int DEFAULT NULL,
  `name` char(20) DEFAULT NULL,
  `gender` char(4) DEFAULT NULL,
  `age` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `student`
--

LOCK TABLES `student` WRITE;
/*!40000 ALTER TABLE `student` DISABLE KEYS */;
INSERT INTO `student` VALUES (1,'Zhangsan','M',21),(2,'Lisi','F',23),(3,'wangwu','F',22),(4,'zhaoliu','M',25);
/*!40000 ALTER TABLE `student` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wordcount`
--

DROP TABLE IF EXISTS `wordcount`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wordcount` (
  `word` char(20) DEFAULT NULL,
  `count` int DEFAULT NULL,
  UNIQUE KEY `word` (`word`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wordcount`
--

LOCK TABLES `wordcount` WRITE;
/*!40000 ALTER TABLE `wordcount` DISABLE KEYS */;
INSERT INTO `wordcount` VALUES ('hello',3),('spark',2),('word',1);
/*!40000 ALTER TABLE `wordcount` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2023-06-08 13:51:33
