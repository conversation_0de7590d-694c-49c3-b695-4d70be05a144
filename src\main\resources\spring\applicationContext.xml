<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
 xmlns:context="http://www.springframework.org/schema/context" 
 xmlns:p="http://www.springframework.org/schema/p"
 xmlns:aop="http://www.springframework.org/schema/aop" 
 xmlns:tx="http://www.springframework.org/schema/tx"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:schemaLocation="http://www.springframework.org/schema/beans
 http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
 http://www.springframework.org/schema/context
 http://www.springframework.org/schema/context/spring-context-4.2.xsd
 http://www.springframework.org/schema/aop 
 http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
 http://www.springframework.org/schema/tx 
 http://www.springframework.org/schema/tx/spring-tx-4.2.xsd
 http://www.springframework.org/schema/util 
 http://www.springframework.org/schema/util/spring-util-4.2.xsd">
    <!-- 数据库连接池 -->
    <!-- 加载配置文件 -->
    <context:property-placeholder 
		location="classpath:properties/db.properties" />
 <!-- 数据库连接池 -->
 <bean id="dataSource" 
		class="com.alibaba.druid.pool.DruidDataSource"        
		destroy-method="close">
       <property name="url" value="${jdbc.url}" />
       <property name="username" value="${jdbc.username}" />
       <property name="password" value="${jdbc.password}" />
       <property name="driverClassName" value="${jdbc.driver}" />
       <property name="maxActive" value="10" />
       <property name="minIdle" value="5" />
  </bean>
<!-- 让spring管理sqlsessionfactory使用mybatis和spring整合包中的 -->
  <bean id="sqlSessionFactory" 
		class="org.mybatis.spring.SqlSessionFactoryBean">
      <!-- 数据库连接池 -->
      <property name="dataSource" ref="dataSource" />
      <!-- 加载mybatis的全局配置文件 -->
      <property name="configLocation" 
				value="classpath:mybatis/mybatis-config.xml" />
  </bean>
  <!-- 使用扫描包的形式来创建mapper代理对象 -->
  <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
      <property name="basePackage" value="cn.itcast.mapper" />
  </bean>
  <!-- 事务管理器 -->
  <bean id="transactionManager" 
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
      <!-- 数据源 -->
      <property name="dataSource" ref="dataSource" />
  </bean>
  <!-- 通知 -->
  <tx:advice id="txAdvice" transaction-manager="transactionManager">
      <tx:attributes>
          <!-- 传播行为 -->
          <tx:method name="save*" propagation="REQUIRED" />
          <tx:method name="insert*" propagation="REQUIRED" />
          <tx:method name="add*" propagation="REQUIRED" />
          <tx:method name="create*" propagation="REQUIRED" />
          <tx:method name="delete*" propagation="REQUIRED" />
          <tx:method name="update*" propagation="REQUIRED" />
          <tx:method name="find*" 
					propagation="SUPPORTS" 
					read-only="true" />
              <tx:method name="select*" 
						propagation="SUPPORTS" 
						read-only="true" />
              <tx:method name="get*" 
						propagation="SUPPORTS" 
						read-only="true" />
          </tx:attributes>
      </tx:advice>
      <!-- 切面 -->
      <aop:config>
          <aop:advisor advice-ref="txAdvice" 
						pointcut="execution(* cn.itcast.service..*.*(..))" />
      </aop:config>
      <!-- 配置包扫描器，扫描所有带@Service注解的类 -->
      <context:component-scan base-package="cn.itcast.service" />
</beans>
